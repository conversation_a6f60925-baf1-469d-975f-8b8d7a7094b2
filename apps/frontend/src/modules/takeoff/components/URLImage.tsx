import { Image as KonvaImage, Rect, Text, Circle, Group } from 'react-konva';
import useImage from 'use-image';
import { URLImageProps } from '../types/drawing-types';
import { useEffect, useState } from 'react';
import { useTakeoffStore } from '../store/takeoff-store';

/**
 * Component to display an image from a URL in a Konva canvas
 */
export function URLImage({
  src,
  width,
  height,
  handleStageClick,
  handleCanvasContextMenu,
}: URLImageProps) {
  // Only try to load image if src is provided
  // Using proper CORS parameters with empty src guard
  const { setIsCanvasLoaded } = useTakeoffStore((state) => state);
  // const [image, status] = useImage(src && src.trim() !== '' ? src : '');
  const [image, status] = useImage(src && src.trim() !== '' ? src : '');
  const [animationFrame, setAnimationFrame] = useState(0);

  useEffect(() => {
    if (src && status === 'loaded') {
      setIsCanvasLoaded(true);
    } else {
      setIsCanvasLoaded(false);
    }
  }, [setIsCanvasLoaded, src, status]);

  // Animation effect for loading spinner
  useEffect(() => {
    if (status === 'loading') {
      const interval = setInterval(() => {
        setAnimationFrame((prev) => (prev + 1) % 360);
      }, 16); // ~60fps
      return () => clearInterval(interval);
    }
  }, [status]);

  // Display the image to fill the entire canvas (architectural plan should cover the full drawable area)
  const getImageDimensions = () => {
    return {
      width,
      height,
      x: 0,
      y: 0,
    };
  };

  // Only calculate dimensions if image is loaded
  const imageDims = image
    ? getImageDimensions()
    : { width: 0, height: 0, x: 0, y: 0 };

  // Return the appropriate component based on loading status
  if (status === 'loaded' && image) {
    return (
      <KonvaImage
        image={image}
        width={imageDims.width}
        height={imageDims.height}
        x={imageDims.x}
        y={imageDims.y}
        onClick={handleStageClick}
        onTap={handleStageClick}
        onContextMenu={handleCanvasContextMenu}
      />
    );
  }

  // Loading or error state with more detailed error message
  const errorMessage =
    !src || src.trim() === ''
      ? 'No image source provided'
      : status === 'loading'
        ? 'Loading Worksheet...'
        : `Error Loading Worksheet${src ? ': ' + src.substring(0, 30) + '...' : ''}`;

  // Calculate card dimensions and position (centered)
  const cardWidth = Math.min(400, width * 0.6);
  const cardHeight = 120;
  const cardX = (width - cardWidth) / 2;
  const cardY = (height - cardHeight) / 2;

  return (
    <Group>
      {/* Main card background with modern styling */}
      <Rect
        x={cardX}
        y={cardY}
        width={cardWidth}
        height={cardHeight}
        fill="white"
        cornerRadius={12}
        shadowColor="rgba(0, 0, 0, 0.1)"
        shadowBlur={20}
        shadowOffset={{ x: 0, y: 4 }}
        shadowOpacity={1}
        stroke="rgba(0, 0, 0, 0.08)"
        strokeWidth={1}
        onClick={handleStageClick}
        onTap={handleStageClick}
        onContextMenu={handleCanvasContextMenu}
      />

      {/* Loading spinner (only for loading state) */}
      {status === 'loading' && (
        <>
          {/* Spinner track */}
          <Circle
            x={cardX + 40}
            y={cardY + cardHeight / 2}
            radius={12}
            stroke="rgba(96, 165, 250, 0.2)"
            strokeWidth={3}
            onClick={handleStageClick}
            onTap={handleStageClick}
            onContextMenu={handleCanvasContextMenu}
          />
          {/* Animated spinner */}
          <Circle
            x={cardX + 40}
            y={cardY + cardHeight / 2}
            radius={12}
            stroke="rgb(96, 165, 250)"
            strokeWidth={3}
            dash={[18, 6]}
            dashOffset={-animationFrame / 10}
            onClick={handleStageClick}
            onTap={handleStageClick}
            onContextMenu={handleCanvasContextMenu}
          />
        </>
      )}

      {/* Error icon (for error state) */}
      {status === 'failed' && (
        <>
          <Circle
            x={cardX + 40}
            y={cardY + cardHeight / 2}
            radius={12}
            fill="rgb(239, 68, 68)"
            onClick={handleStageClick}
            onTap={handleStageClick}
            onContextMenu={handleCanvasContextMenu}
          />
          <Text
            x={cardX + 40 - 6}
            y={cardY + cardHeight / 2 - 8}
            width={12}
            height={16}
            text="!"
            fontSize={16}
            fontFamily="Geist, -apple-system, BlinkMacSystemFont, sans-serif"
            fontStyle="bold"
            fill="white"
            align="center"
            onClick={handleStageClick}
            onTap={handleStageClick}
            onContextMenu={handleCanvasContextMenu}
          />
        </>
      )}

      {/* Main text */}
      <Text
        x={cardX + 80}
        y={cardY + cardHeight / 2 - 20}
        width={cardWidth - 100}
        height={20}
        text={errorMessage}
        fontSize={16}
        fontFamily="Geist, -apple-system, BlinkMacSystemFont, sans-serif"
        fontStyle="600"
        fill={status === 'loading' ? 'rgb(15, 23, 42)' : 'rgb(239, 68, 68)'}
        align="left"
        verticalAlign="middle"
        onClick={handleStageClick}
        onTap={handleStageClick}
        onContextMenu={handleCanvasContextMenu}
      />

      {/* Subtitle text (only for loading state) */}
      {status === 'loading' && (
        <Text
          x={cardX + 80}
          y={cardY + cardHeight / 2 + 5}
          width={cardWidth - 100}
          height={16}
          text="Please wait while we prepare your blueprint..."
          fontSize={14}
          fontFamily="Geist, -apple-system, BlinkMacSystemFont, sans-serif"
          fill="rgb(100, 116, 139)"
          align="left"
          verticalAlign="middle"
          onClick={handleStageClick}
          onTap={handleStageClick}
          onContextMenu={handleCanvasContextMenu}
        />
      )}
    </Group>
  );
}
