import { Drawing } from '../types/drawing';
import { DrawingShape } from '../types/drawing-types';
import {
  ScaleInfo,
  PaperSize,
  getRectangleMeasurementsPure,
  getCircleMeasurements,
  getEllipseMeasurements,
  getFreehandMeasurementsPure,
  getFreehandSurfaceMeasurements,
  getPointToPointMeasurementsPure,
  getPointToPointSurfaceMeasurements,
  REFERENCE_CANVAS_DIMENSIONS,
} from '@repo/component-summary';
import { DRAWING_COLORS, STROKE_WIDTHS } from '../constants/drawing';

export interface MeasurementData {
  type: 'area' | 'length' | 'count';
  primary: string; // Main measurement (area or length)
  secondary?: string; // Secondary measurement (perimeter for closed shapes)
  details?: string[]; // Additional details like radius, width, height
}

/**
 * Convert API drawing to DrawingShape format for measurement calculations
 */
function apiDrawingToMeasurementShape(drawing: Drawing): DrawingShape | null {
  const { config } = drawing;

  switch (config.type) {
    case 'rectangle':
      return {
        type: 'rectangle',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        width: parseFloat(config.width || '0'),
        height: parseFloat(config.height || '0'),
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    case 'circle':
      return {
        type: 'circle',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        radius: parseFloat(config.radius || '0'),
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    case 'ellipse':
      return {
        type: 'ellipse',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        radiusX: parseFloat(config.radiusX || '0'),
        radiusY: parseFloat(config.radiusY || '0'),
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    case 'freehand':
      const freehandPoints = JSON.parse(config.points || '[]');
      return {
        type: 'freehand',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        points: freehandPoints,
        closed: config.closed === 'true',
        tension: parseFloat(config.tension || '0.3'),
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    case 'curve':
      const curvePoints = JSON.parse(config.points || '[]');
      return {
        type: 'curve',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        points: curvePoints,
        previewPoints: [], // No preview for saved drawings
        isDrawing: false,
        tension: parseFloat(config.tension || '0.3'),
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    case 'point-to-point':
      const p2pPoints = JSON.parse(config.points || '[]');
      return {
        type: 'point-to-point',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        points: p2pPoints,
        previewPoints: [], // No preview for saved drawings
        closed: config.closed === 'true',
        isDrawing: false,
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    case 'point':
      return {
        type: 'point',
        id: `shape-${drawing.id}`,
        x: parseFloat(config.x || '0'),
        y: parseFloat(config.y || '0'),
        points: JSON.parse(config.points || '[]'),
        closed: config.closed === 'true',
        pointType:
          (config.pointType as 'circle' | 'square' | 'triangle') || 'circle',
        fill: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        stroke: config.fill || DRAWING_COLORS.DEFAULT_STROKE,
        strokeWidth: parseFloat(
          config.strokeWidth || STROKE_WIDTHS.THIN.toString(),
        ),
      };

    default:
      console.warn('Unknown drawing type for measurements:', config.type);
      return null;
  }
}

/**
 * Get measurements for a drawing based on its type and properties
 * Reuses the same logic as LiveMeasurement component
 */
export function getDrawingMeasurements(
  drawing: Drawing,
  scale: ScaleInfo,
  paperSize: PaperSize,
): MeasurementData | null {
  const shape = apiDrawingToMeasurementShape(drawing);
  if (!shape) return null;

  switch (shape.type) {
    case 'rectangle': {
      // Only show measurements if the rectangle has some size
      if (Math.abs(shape.width) < 1 || Math.abs(shape.height) < 1) {
        return null;
      }

      // Use pure mathematical approach for 100% accuracy
      const measurements = getRectangleMeasurementsPure(
        shape.width,
        shape.height,
        scale,
        paperSize,
        REFERENCE_CANVAS_DIMENSIONS,
      );

      return {
        type: 'area',
        primary: measurements.area.formatted,
        secondary: measurements.perimeter.formatted,
        details: [
          `Width: ${measurements.width.formatted}`,
          `Height: ${measurements.height.formatted}`,
        ],
      };
    }

    case 'circle': {
      // Only show measurements if the circle has some radius
      if (Math.abs(shape.radius) < 1) {
        return null;
      }

      const measurements = getCircleMeasurements(
        shape.radius,
        scale,
        paperSize,
      );

      return {
        type: 'area',
        primary: measurements.area.formatted,
        secondary: measurements.circumference.formatted,
        details: [
          `Radius: ${measurements.radius.formatted}`,
          `Diameter: ${measurements.diameter.formatted}`,
        ],
      };
    }

    case 'ellipse': {
      // Only show measurements if the ellipse has some size
      if (Math.abs(shape.radiusX) < 1 || Math.abs(shape.radiusY) < 1) {
        return null;
      }

      const measurements = getEllipseMeasurements(
        shape.radiusX,
        shape.radiusY,
        scale,
        paperSize,
      );

      return {
        type: 'area',
        primary: measurements.area.formatted,
        secondary: measurements.perimeter.formatted,
        details: [
          `Radius X: ${measurements.radiusX.formatted}`,
          `Radius Y: ${measurements.radiusY.formatted}`,
        ],
      };
    }

    case 'freehand': {
      // Show measurements if we have enough points
      if (shape.points.length < 4) {
        return null;
      }

      if (!shape.closed) {
        // Edge component - show length with directional accuracy
        const measurements = getFreehandMeasurementsPure(
          shape.points,
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );

        return {
          type: 'length',
          primary: measurements.length.formatted,
        };
      } else {
        // Surface component - show area
        const measurements = getFreehandSurfaceMeasurements(
          shape.points,
          scale,
          paperSize,
        );

        return {
          type: 'area',
          primary: measurements.area.formatted,
          secondary: measurements.perimeter.formatted,
        };
      }
    }

    case 'curve': {
      // Show measurements if we have enough points (curves are always edge components)
      if (shape.points.length < 4) {
        return null;
      }

      const measurements = getFreehandMeasurementsPure(
        shape.points,
        scale,
        paperSize,
        REFERENCE_CANVAS_DIMENSIONS,
      );

      return {
        type: 'length',
        primary: measurements.length.formatted,
      };
    }

    case 'point-to-point': {
      // Show measurements if we have enough points
      if (shape.points.length < 4) {
        return null;
      }

      if (!shape.closed) {
        // Edge component - show length with directional accuracy
        const measurements = getPointToPointMeasurementsPure(
          shape.points,
          shape.previewPoints,
          scale,
          paperSize,
          REFERENCE_CANVAS_DIMENSIONS,
        );

        return {
          type: 'length',
          primary: measurements.length.formatted,
        };
      } else {
        // Surface component - show area
        const measurements = getPointToPointSurfaceMeasurements(
          shape.points,
          shape.previewPoints,
          scale,
          paperSize,
        );

        return {
          type: 'area',
          primary: measurements.area.formatted,
          secondary: measurements.perimeter.formatted,
        };
      }
    }
    case 'point': {
      return {
        type: 'count',
        primary: '1',
      };
    }

    default:
      return null;
  }
}
