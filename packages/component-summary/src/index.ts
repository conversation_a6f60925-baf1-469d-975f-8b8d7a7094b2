/**
 * @repo/component-summary
 *
 * A shared package for calculating component measurements and summaries
 * from blueprint drawings. Used by both frontend and backend applications.
 */

// Export all types
export * from './types';

// Export constants
export * from './constants';

// Export main API functions
export {
  generateComponentSummary,
  generateFilteredComponentSummary,
  createDrawingSummaryItem,
  generateDrawingDisplayId,
  calculateProjectTotals,
  formatMeasurement,
  filterDataForExport,
} from './core/summary-generator';

// Export core calculation functions
export {
  calculateDrawingMeasurements,
  extractDrawingMeasurementDynamic,
} from './core/drawing-calculator';

export { calculateComponentTotalMeasurements } from './core/component-aggregator';

// Export utility functions (for advanced use cases)
export {
  pixelsToRealWorld,
  calculatePixelsPerDrawingUnit,
  calculateDistance,
  calculateRectanglePerimeter,
  calculateRectangleArea,
  calculateCircleCircumference,
  calculateCircleArea,
  calculateEllipsePerimeter,
  calculateEllipseArea,
  calculateLineLength,
  calculatePolygonArea,
  getRectangleMeasurements,
  getRectangleMeasurementsPure,
  drawingPixelsToRealWorldDirect,
  drawingPixelsToRealWorldPure,
  calculateLineLengthDirectional,
  detectLineDirection,
  getCircleMeasurements,
  getCircleMeasurementsPure,
  getEllipseMeasurements,
  getFreehandMeasurements,
  getFreehandMeasurementsPure,
  getFreehandSurfaceMeasurements,
  getPointToPointMeasurements,
  getPointToPointMeasurementsPure,
  getPointToPointSurfaceMeasurements,
} from './utils/distance-utils';
