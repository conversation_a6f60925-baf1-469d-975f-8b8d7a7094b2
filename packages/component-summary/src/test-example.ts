/**
 * Example usage of the component summary package
 * This file demonstrates how to use the package functions
 */

import {
  generateComponentSummary,
  calculateProjectTotals,
  Component,
  Drawing,
  ScaleInfo,
  PaperSize,
  DEFAULT_PAPER_SIZE,
  pixelsToRealWorld,
  calculatePixelsPerDrawingUnit,
} from './index';

// Example data
const mockScale: ScaleInfo = {
  num_metric: 1,
  num_unit: 'inch',
  den_metric: 8,
  den_unit: 'ft',
};

const mockPaperSize: PaperSize = DEFAULT_PAPER_SIZE;

const mockComponents: Component[] = [
  {
    id: 1,
    name: 'Wall Component',
    selectionType: 'allPages',
    blueprintFileId: 'file-1',
    geometryType: 'surface',
    color: '#ff0000',
    shade: 'solid',
  },
  {
    id: 2,
    name: 'Door Component',
    selectionType: 'allPages',
    blueprintFileId: 'file-1',
    geometryType: 'edge',
    color: '#00ff00',
    shade: 'solid',
  },
];

const mockDrawings: Drawing[] = [
  {
    id: 1,
    blueprintImageId: 'image-1',
    componentId: 1,
    config: {
      type: 'rectangle',
      width: '100',
      height: '50',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    deletedAt: null,
  },
  {
    id: 2,
    blueprintImageId: 'image-1',
    componentId: 2,
    config: {
      type: 'freehand',
      points: JSON.stringify([0, 0, 100, 0, 100, 100, 0, 100]),
      closed: 'false',
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    deletedAt: null,
  },
];

// Test the package functions
export function testPackage() {
  console.log('Testing @repo/component-summary package...');

  // Generate component summary
  const summaryData = generateComponentSummary(
    mockComponents,
    mockDrawings,
    mockScale,
    mockPaperSize,
  );

  console.log('Generated summary data:');
  for (const [componentId, data] of summaryData.entries()) {
    console.log(`Component ${componentId}:`, {
      name: data.componentName,
      type: data.componentType,
      totalMetrics: data.totalMetrics,
      drawingCount: data.drawings.length,
    });
  }

  // Calculate project totals
  const totals = calculateProjectTotals(summaryData);
  console.log('Project totals:', totals);

  return {
    summaryData,
    totals,
  };
}

// Test function to debug measurement calculations
export function testMeasurementCalculations() {
  console.log('=== DEBUGGING MEASUREMENT CALCULATIONS ===');

  // Your scale: 1/4" = 1' (1 inch on drawing = 4 feet in real world)
  const userScale: ScaleInfo = {
    num_metric: 0.25, // 1/4 inch
    num_unit: 'inch',
    den_metric: 1,
    den_unit: 'ft',
  };

  // ARCH D paper size: 36" x 24"
  const archDPaper: PaperSize = {
    width: 36,
    height: 24,
    unit: 'inch',
  };

  console.log('Scale:', userScale);
  console.log('Paper size:', archDPaper);
  console.log('Canvas dimensions:', { width: 900, height: 500 });

  // Test the pixel-to-real-world conversion
  const pixelsPerDrawingUnit = calculatePixelsPerDrawingUnit(
    userScale,
    archDPaper,
  );
  console.log('Pixels per drawing unit:', pixelsPerDrawingUnit);

  // Test specific measurements from your image
  // Height: actual 15'0.5", calculated 13.65 ft
  // Width: actual 14'6.5", calculated 15.84 ft

  // Let's reverse engineer what pixel values would give us the actual measurements
  const actualHeight = 15 + 0.5 / 12; // 15'0.5" in decimal feet
  const actualWidth = 14 + 6.5 / 12; // 14'6.5" in decimal feet

  console.log('Expected measurements:');
  console.log('Height:', actualHeight, 'ft');
  console.log('Width:', actualWidth, 'ft');

  // Calculate what pixel values should give us these measurements
  const heightPixelsNeeded =
    (actualHeight * pixelsPerDrawingUnit) / userScale.den_metric;
  const widthPixelsNeeded =
    (actualWidth * pixelsPerDrawingUnit) / userScale.den_metric;

  console.log('Pixel values needed for correct measurements:');
  console.log('Height pixels needed:', heightPixelsNeeded);
  console.log('Width pixels needed:', widthPixelsNeeded);

  // Test with some sample pixel values to see what we get
  const testHeightPixels = 300; // Example
  const testWidthPixels = 350; // Example

  const heightMeasurement = pixelsToRealWorld(
    testHeightPixels,
    userScale,
    archDPaper,
  );
  const widthMeasurement = pixelsToRealWorld(
    testWidthPixels,
    userScale,
    archDPaper,
  );

  console.log('Test measurements with sample pixels:');
  console.log(`Height (${testHeightPixels}px):`, heightMeasurement);
  console.log(`Width (${testWidthPixels}px):`, widthMeasurement);

  return {
    userScale,
    archDPaper,
    pixelsPerDrawingUnit,
    actualHeight,
    actualWidth,
    heightPixelsNeeded,
    widthPixelsNeeded,
    testMeasurements: {
      height: heightMeasurement,
      width: widthMeasurement,
    },
  };
}
