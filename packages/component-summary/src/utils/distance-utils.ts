/**
 * Utility functions for calculating real-world distances from pixel measurements
 * using the blueprint scale information
 */

import {
  PaperSize,
  ScaleInfo,
  Unit,
  DistanceMeasurement,
  DimensionInfo,
} from '../types';
import { REFERENCE_CANVAS_DIMENSIONS } from '../constants';

/**
 * Calculate pixels per drawing unit based on dynamic paper size and scale
 * The image canvas (900x500 pixels) represents the current paper size from store
 * This ensures measurements remain consistent regardless of viewport changes
 *
 * @param scale - Scale information
 * @param paperSize - Paper size information
 * @param direction - Optional direction ('x' or 'y') for directional accuracy
 */
export function calculatePixelsPerDrawingUnit(
  scale: ScaleInfo,
  paperSize: PaperSize,
  direction?: 'x' | 'y',
): number {
  // Get current paper size from store
  const currentPaperSize = paperSize;

  // The image canvas represents the current paper size
  const imagePixelDimensions = REFERENCE_CANVAS_DIMENSIONS;

  // Convert paper size to inches for consistent calculations using existing function
  const paperWidthInches = convertToInches(
    currentPaperSize.width,
    currentPaperSize.unit,
  );
  const paperHeightInches = convertToInches(
    currentPaperSize.height,
    currentPaperSize.unit,
  );

  // Calculate pixels per inch based on image representing the current paper size
  const pixelsPerInchX = imagePixelDimensions.width / paperWidthInches;
  const pixelsPerInchY = imagePixelDimensions.height / paperHeightInches;

  // Use direction-specific pixels per inch for accuracy, or average for backward compatibility
  let pixelsPerInch: number;
  if (direction === 'x') {
    pixelsPerInch = pixelsPerInchX;
  } else if (direction === 'y') {
    pixelsPerInch = pixelsPerInchY;
  } else {
    // For backward compatibility, use the more accurate X direction as default
    // since most measurements are horizontal and X direction typically has higher resolution
    pixelsPerInch = pixelsPerInchX;
  }

  // Convert scale to pixels per drawing unit
  // Scale format: num_metric num_unit = den_metric den_unit
  // Example: 1 cm = 8 ft means 1 cm on drawing = 8 ft in real world

  // First, convert numerator unit (drawing unit) to inches
  const drawingUnitsPerInch = convertToInches(1, scale.num_unit);

  // Calculate pixels per drawing unit
  const pixelsPerDrawingUnit = pixelsPerInch / drawingUnitsPerInch;

  return pixelsPerDrawingUnit;
}

/**
 * Convert pixels to real-world distance based on scale and canvas dimensions
 * Scale format: num_metric num_unit = den_metric den_unit
 * Example: 1 cm = 8 ft means 1 pixel (representing 1 cm on screen) = 8 ft in real world
 *
 * @param pixels - Number of pixels to convert
 * @param scale - Scale information
 * @param paperSize - Paper size information
 * @param targetUnit - Optional target unit for conversion
 * @param direction - Optional direction ('x' or 'y') for directional accuracy
 */
export function pixelsToRealWorld(
  pixels: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
  targetUnit?: Unit,
  direction?: 'x' | 'y',
): DistanceMeasurement {
  // Calculate pixels per drawing unit dynamically based on canvas
  const pixelsPerDrawingUnit = calculatePixelsPerDrawingUnit(
    scale,
    paperSize,
    direction,
  );

  // Calculate the real-world distance per pixel
  const realWorldPerPixel = scale.den_metric / pixelsPerDrawingUnit;

  // Calculate total real-world distance in denominator units
  const realWorldInDenUnit = pixels * realWorldPerPixel;

  // Convert to target unit or use the scale's denominator unit (real-world unit)
  const outputUnit = targetUnit || scale.den_unit;

  // Convert from denominator unit to target unit
  const realWorldInTargetUnit = convertBetweenUnits(
    realWorldInDenUnit,
    scale.den_unit,
    outputUnit,
  );

  return {
    pixels,
    realWorld: realWorldInTargetUnit,
    unit: outputUnit,
    formatted: formatDistance(realWorldInTargetUnit, outputUnit),
  };
}

/**
 * Calculate distance between two points
 */
export function calculateDistance(
  point1: { x: number; y: number },
  point2: { x: number; y: number },
): number {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Calculate perimeter of a rectangle
 */
export function calculateRectanglePerimeter(
  width: number,
  height: number,
): number {
  return 2 * (Math.abs(width) + Math.abs(height));
}

/**
 * Calculate area of a rectangle
 */
export function calculateRectangleArea(width: number, height: number): number {
  return Math.abs(width) * Math.abs(height);
}

/**
 * Calculate circumference of a circle
 */
export function calculateCircleCircumference(radius: number): number {
  return 2 * Math.PI * Math.abs(radius);
}

/**
 * Calculate area of a circle
 */
export function calculateCircleArea(radius: number): number {
  return Math.PI * Math.abs(radius) * Math.abs(radius);
}

/**
 * Calculate perimeter of an ellipse (Ramanujan's approximation)
 */
export function calculateEllipsePerimeter(
  radiusX: number,
  radiusY: number,
): number {
  const a = Math.abs(radiusX);
  const b = Math.abs(radiusY);
  const h = Math.pow((a - b) / (a + b), 2);
  return Math.PI * (a + b) * (1 + (3 * h) / (10 + Math.sqrt(4 - 3 * h)));
}

/**
 * Calculate area of an ellipse
 */
export function calculateEllipseArea(radiusX: number, radiusY: number): number {
  return Math.PI * Math.abs(radiusX) * Math.abs(radiusY);
}

/**
 * Convert between different units
 */
function convertBetweenUnits(
  value: number,
  fromUnit: Unit,
  toUnit: Unit,
): number {
  if (fromUnit === toUnit) {
    return value;
  }

  // Convert to cm first, then to target unit
  const valueInCm = convertToCm(value, fromUnit);
  return convertFromCm(valueInCm, toUnit);
}

/**
 * Convert distance to inches
 */
function convertToInches(value: number, unit: Unit): number {
  switch (unit) {
    case 'inch':
      return value;
    case 'cm':
      return value / 2.54; // 1 inch = 2.54 cm
    case 'ft':
      return value * 12; // 1 foot = 12 inches
    default:
      return value;
  }
}

/**
 * Convert distance to cm
 */
function convertToCm(value: number, unit: Unit): number {
  switch (unit) {
    case 'cm':
      return value;
    case 'inch':
      return value * 2.54; // 1 inch = 2.54 cm
    case 'ft':
      return value * 30.48; // 1 foot = 30.48 cm
    default:
      return value;
  }
}

/**
 * Convert distance from cm to target unit
 */
function convertFromCm(valueCm: number, targetUnit: Unit): number {
  switch (targetUnit) {
    case 'cm':
      return valueCm;
    case 'inch':
      return valueCm / 2.54;
    case 'ft':
      return valueCm / 30.48;
    default:
      return valueCm;
  }
}

/**
 * Format distance for display
 */
function formatDistance(value: number, unit: string): string {
  // Round to 2 decimal places for display
  const rounded = Math.round(value * 100) / 100;
  return `${rounded} ${unit}`;
}

/**
 * Get measurements for a rectangle shape
 */
export function getRectangleMeasurements(
  width: number,
  height: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  width: DistanceMeasurement;
  height: DistanceMeasurement;
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use directional accuracy: width uses X direction, height uses Y direction
  const widthMeasurement = pixelsToRealWorld(
    Math.abs(width),
    scale,
    paperSize,
    undefined,
    'x',
  );
  const heightMeasurement = pixelsToRealWorld(
    Math.abs(height),
    scale,
    paperSize,
    undefined,
    'y',
  );
  const perimeterPixels = calculateRectanglePerimeter(width, height);
  // For perimeter, use X direction as it's typically more accurate
  const perimeterMeasurement = pixelsToRealWorld(
    perimeterPixels,
    scale,
    paperSize,
    undefined,
    'x',
  );

  // Area calculation (square units)
  const areaPixels = calculateRectangleArea(width, height);
  const areaRealWorld =
    widthMeasurement.realWorld * heightMeasurement.realWorld;

  return {
    width: widthMeasurement,
    height: heightMeasurement,
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${widthMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${widthMeasurement.unit}`,
    },
  };
}

/**
 * Calculate total length of a freehand line from points array
 */
export function calculateLineLength(points: number[]): number {
  let totalLength = 0;
  for (let i = 0; i < points.length - 2; i += 2) {
    const dx = points[i + 2] - points[i];
    const dy = points[i + 3] - points[i + 1];
    totalLength += Math.sqrt(dx * dx + dy * dy);
  }
  return totalLength;
}

/**
 * Get measurements for a freehand line
 */
export function getFreehandMeasurements(
  points: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  length: DistanceMeasurement;
} {
  const lengthPixels = calculateLineLength(points);
  const lengthMeasurement = pixelsToRealWorld(lengthPixels, scale, paperSize);

  return {
    length: lengthMeasurement,
  };
}

/**
 * Get measurements for a circle shape
 */
export function getCircleMeasurements(
  radius: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  radius: DistanceMeasurement;
  diameter: DistanceMeasurement;
  circumference: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  const radiusMeasurement = pixelsToRealWorld(
    Math.abs(radius),
    scale,
    paperSize,
  );
  const diameterMeasurement = pixelsToRealWorld(
    Math.abs(radius) * 2,
    scale,
    paperSize,
  );
  const circumferencePixels = calculateCircleCircumference(radius);
  const circumferenceMeasurement = pixelsToRealWorld(
    circumferencePixels,
    scale,
    paperSize,
  );

  // Area calculation (square units)
  const areaPixels = calculateCircleArea(radius);
  const areaRealWorld =
    Math.PI * radiusMeasurement.realWorld * radiusMeasurement.realWorld;

  return {
    radius: radiusMeasurement,
    diameter: diameterMeasurement,
    circumference: circumferenceMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${radiusMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${radiusMeasurement.unit}`,
    },
  };
}

/**
 * Get measurements for an ellipse shape
 */
export function getEllipseMeasurements(
  radiusX: number,
  radiusY: number,
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  radiusX: DistanceMeasurement;
  radiusY: DistanceMeasurement;
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use directional accuracy: radiusX uses X direction, radiusY uses Y direction
  const radiusXMeasurement = pixelsToRealWorld(
    Math.abs(radiusX),
    scale,
    paperSize,
    undefined,
    'x',
  );
  const radiusYMeasurement = pixelsToRealWorld(
    Math.abs(radiusY),
    scale,
    paperSize,
    undefined,
    'y',
  );
  const perimeterPixels = calculateEllipsePerimeter(radiusX, radiusY);
  // For perimeter, use X direction as it's typically more accurate
  const perimeterMeasurement = pixelsToRealWorld(
    perimeterPixels,
    scale,
    paperSize,
    undefined,
    'x',
  );

  // Area calculation (square units)
  const areaPixels = calculateEllipseArea(radiusX, radiusY);
  const areaRealWorld =
    Math.PI * radiusXMeasurement.realWorld * radiusYMeasurement.realWorld;

  return {
    radiusX: radiusXMeasurement,
    radiusY: radiusYMeasurement,
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${radiusXMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${radiusXMeasurement.unit}`,
    },
  };
}

/**
 * Calculate area of a closed polygon using the shoelace formula
 */
export function calculatePolygonArea(points: number[]): number {
  if (points.length < 6) return 0; // Need at least 3 points (6 coordinates)

  let area = 0;
  const n = points.length / 2;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    const xi = points[i * 2];
    const yi = points[i * 2 + 1];
    const xj = points[j * 2];
    const yj = points[j * 2 + 1];

    area += xi * yj - xj * yi;
  }

  return Math.abs(area) / 2;
}

/**
 * Get measurements for a freehand surface (surface component)
 */
export function getFreehandSurfaceMeasurements(
  points: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Calculate perimeter (including closing line back to start)
  const perimeterPixels =
    calculateLineLength(points) +
    Math.sqrt(
      Math.pow(points[0] - points[points.length - 2], 2) +
        Math.pow(points[1] - points[points.length - 1], 2),
    );
  const perimeterMeasurement = pixelsToRealWorld(
    perimeterPixels,
    scale,
    paperSize,
  );

  // Calculate area using shoelace formula
  const areaPixels = calculatePolygonArea(points);
  const areaRealWorld =
    areaPixels * Math.pow(perimeterMeasurement.realWorld / perimeterPixels, 2);

  return {
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${perimeterMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${perimeterMeasurement.unit}`,
    },
  };
}

/**
 * Get measurements for a point-to-point line (edge component)
 */
export function getPointToPointMeasurements(
  points: number[],
  previewPoints: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  length: DistanceMeasurement;
} {
  // Use preview points if available (during drawing), otherwise use confirmed points
  const pointsToUse = previewPoints.length > 0 ? previewPoints : points;

  const lengthPixels = calculateLineLength(pointsToUse);
  const lengthMeasurement = pixelsToRealWorld(lengthPixels, scale, paperSize);

  return {
    length: lengthMeasurement,
  };
}

/**
 * Get measurements for a point-to-point surface (surface component)
 */
export function getPointToPointSurfaceMeasurements(
  points: number[],
  previewPoints: number[],
  scale: ScaleInfo,
  paperSize: PaperSize,
): {
  perimeter: DistanceMeasurement;
  area: { pixels: number; realWorld: number; unit: string; formatted: string };
} {
  // Use preview points if available (during drawing), otherwise use confirmed points
  const pointsToUse = previewPoints.length > 0 ? previewPoints : points;

  // For surface components, we need to close the shape for proper measurement
  // If preview points don't already include closing line, we need to calculate it
  const closedPoints = [...pointsToUse];

  // Check if the shape is already closed (last point equals first point)
  const isAlreadyClosed =
    pointsToUse.length >= 4 &&
    pointsToUse[0] === pointsToUse[pointsToUse.length - 2] &&
    pointsToUse[1] === pointsToUse[pointsToUse.length - 1];

  if (!isAlreadyClosed && pointsToUse.length >= 4) {
    // Add closing line back to start for perimeter calculation
    closedPoints.push(pointsToUse[0], pointsToUse[1]);
  }

  // Calculate perimeter using the closed points
  const perimeterPixels = calculateLineLength(closedPoints);
  const perimeterMeasurement = pixelsToRealWorld(
    perimeterPixels,
    scale,
    paperSize,
  );

  // Calculate area using shoelace formula on the original points (not including closing line)
  const areaPixels = calculatePolygonArea(pointsToUse);
  const areaRealWorld =
    areaPixels * Math.pow(perimeterMeasurement.realWorld / perimeterPixels, 2);

  return {
    perimeter: perimeterMeasurement,
    area: {
      pixels: areaPixels,
      realWorld: areaRealWorld,
      unit: `sq ${perimeterMeasurement.unit}`,
      formatted: `${Math.round(areaRealWorld * 100) / 100} sq ${perimeterMeasurement.unit}`,
    },
  };
}
